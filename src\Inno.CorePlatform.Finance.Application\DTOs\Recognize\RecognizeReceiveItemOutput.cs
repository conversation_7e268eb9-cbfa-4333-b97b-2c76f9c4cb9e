using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using MassTransit.NewIdProviders;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Recognize
{
    public class RecognizeReceiveItemOutPut
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 认款单号
        /// </summary>      
        public string Code { get; set; }
        /// <summary>
        /// 单号日期
        /// </summary>     
        public DateTime BillDate { get; set; }
        /// <summary>
        /// 收款单号
        /// </summary>  
        public string ReceiveCode { get; set; }
        /// <summary>
        /// 本次认款金额
        /// </summary>   
        public decimal Value { get; set; }
        /// <summary>
        /// 收款金额
        /// </summary>       
        public decimal ReceiveValue { get; set; }
        /// <summary>
        /// 收款单位Id
        /// </summary>    
        public string CompanyId { get; set; }
        /// <summary>
        /// 收款单位名称
        /// </summary>  
        public string CompanyName { get; set; }
        /// <summary>
        /// 付款单位Id
        /// </summary>   
        public string CustomerId { get; set; }
        /// <summary>
        /// 付款单位名称
        /// </summary> 
        public string CustomerNme { get; set; }
        /// <summary>
        /// 收款类型
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// 收款时间
        /// </summary>
        public DateTime ReceiveDate { get; set; }
        /// <summary>
        /// 批量附件Id
        /// </summary>    
        public string AttachFileIds { get; set; }
        /// <summary>
        /// 核算部门Id路径
        /// </summary>     
        public string BusinessDeptFullPath { get; set; }
        /// <summary>
        /// 核算部门名称路径
        /// </summary>   
        public string BusinessDeptFullName { get; set; }
        /// <summary>
        /// 核算部门当前Id
        /// </summary>       
        public int? BusinessDepId { get; set; }
        /// <summary>
        /// 项目编码
        /// </summary>   
        public string ProjectCode { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary> 
        public string ProjectName { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public RecognizeReceiveItemStatusEnum Status { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        public RecognizeReceiveClassifyEnum Classify { get; set; }
        /// <summary>
        /// 状态描述
        /// </summary>
        public string? StatusDescription { get { return this.Status.GetDescription(); } }
        /// <summary>
        /// 类型描述
        /// </summary>
        public string? ClassifyDescription { get { return this.Classify.GetDescription(); } }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTimeOffset CreatedTime { get; set; }

        public string? CreatedBy { get; set; }

        public string? RelateCode { get; set; }
        public List<string> Receivedetailcode { get; set; }

        public Guid? ServiceId { get; set; }
        /// <summary>
        /// 结算方式 
        /// </summary>  
        public string? Settletype { get; set; }

        /// <summary>
        /// 到期日 
        /// </summary>  
        public DateTime? DraftBillExpireDate { get; set; }

        /// <summary>
        /// 剩余可认款金额
        /// </summary>   
        public decimal RemainingRecognizableAmount { get; set; }

        public IEnumerable<string?> ActualCustomerIds { get; internal set; }

        /// <summary>
        /// 银行账户
        /// </summary>
        public string? BankNum { get; set; }
        /// <summary>
        /// 银行类型(名称)
        /// </summary>
        public string? BankName { get; set; }
        /// <summary>
        /// 贴现日期
        /// </summary>
        public DateTime? DiscountDate { get; set; }

        /// <summary>
        /// 交易时间
        /// </summary>
        public DateTime? BizTime { get; set; }

        /// <summary>
        /// 转货款状态 1=已转货款，0=未转货款
        /// </summary>
        public int TransferStatus { get; set; }

        /// <summary>
        /// 转货款状态描述
        /// </summary>
        public string TransferStatusDescription
        {
            get
            {
                return TransferStatus == 1 ? "已转货款" : "未转货款";
            }
        }
    }

    public class RecognizeReceiveItemTabCount
    {

        /// <summary>
        /// 已撤销数量
        /// </summary>       
        public int? auditingCount { get; set; }

        /// <summary>
        /// 待提交数量
        /// </summary>       
        public int? waitSubmitCount { get; set; }

        /// <summary>
        /// 待执行数量
        /// </summary>       
        public int? waitExecuteCount { get; set; }

        /// <summary>
        /// 已完成数量
        /// </summary>       
        public int? completedCount { get; set; }
        /// <summary>
        /// 全部数量
        /// </summary>       
        public int? allCount { get; set; }

        /// <summary>
        /// 部分撤销
        /// </summary>
        public int? partCancel { get; set; }
    }

    public class RemainingRecognizableAmountOutput
    {
        /// <summary>
        /// 单号
        /// </summary>       
        public string? Code { get; set; }

        /// <summary>
        /// 剩余可认款金额
        /// </summary>       
        public decimal? RemainingRecognizableAmount { get; set; }
    }

    public class RecognizeReceiveDetailsByTypeOutput
    {
        public Guid? Id { get; set; }
        /// <summary>
        /// 认款单Id
        /// </summary>
        public Guid RecognizeReceiveItemId { get; set; }
        public string? BusinessId { get; set; }

        public string? CustomerId { get; set; }
        public string? CustomerName { get; set; }
        /// <summary>
        /// 终端客户名
        /// </summary>
        public string? HospitalName { get; set; }
        /// <summary>
        /// 终端客户ID
        /// </summary>
        public string? HospitalId { get; set; }
        public DateTime? InvoiceTime { get; set; }

        public decimal? Amount { get; set; }

        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 细分类型
        /// </summary>
        public RecognizeReceiveDetailClassifyEnum? Classify { get; set; }

        /// <summary>
        /// 项目编码
        /// </summary>
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 可认款金额
        /// </summary>
        public decimal? CanAmount { get; set; }
        public string? Remark { get; set; }


        /// <summary>
        /// 是否跳号
        /// </summary>
        public bool IsSkip { get; set; }

        /// <summary>
        /// 认款类型
        /// </summary>
        public int? Type { get; set; }

        /// <summary>
        /// 认款明细列表对应应收信息
        /// </summary>
        public List<CreditInfo>? CreditInfo { get; set; }
    }

    /// <summary>
    /// 认款明细列表对应应收信息
    /// </summary>
    public class CreditInfo
    {
        /// <summary>
        /// 应收id
        /// </summary>
        public Guid? CreditId { get; set; }
        /// <summary>
        /// 认款明细code
        /// </summary>
        public string? RecognizeReceiveDetailCode { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary> 
        public string? BillCode { get; set; }

        /// <summary>
        /// 应收类型
        /// </summary>
        public CreditTypeEnum? CreditType { get; set; }

        /// <summary>
        /// 应收类型
        /// </summary>
        public string CreditTypeStr
        {
            get
            {
                return CreditType.HasValue ? CreditType.GetDescription() : string.Empty;
            }
        }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public string BillDateStr
        {
            get
            {
                return BillDate.HasValue ? BillDate.Value.ToString("yyyy-MM-dd") : string.Empty;
            }
        }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 项目单号 
        /// </summary> 
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称 
        /// </summary> 
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 业务单元Id
        /// </summary> 
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 应收值
        /// </summary>
        public decimal? Value { get; set; }

        /// <summary>
        /// 对应发票可认款金额
        /// </summary>
        public decimal? SurplusValue { get; set; }

        /// <summary>
        /// 本次认款金额
        /// </summary>
        public decimal? CurrentValue { get; set; }

        /// <summary>
        /// 关联金额（应收关联发票或订单的金额）
        /// </summary>
        public decimal? AssociationValue { get; set; }
    }

    /// <summary>
    /// 认款明细查询应收入参
    /// </summary>
    public class RecognizeReceiveCreditInput
    {
        /// <summary>
        /// 认款类型 1=发票，2=订单，3=初始应收
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 认款明细单号
        /// </summary>
        public List<string?> Codes { get; set; }
    }

    /// <summary>
    /// 认款部分信息（公司客户认款单id以及应收单号）
    /// </summary>
    public class RecognizeReceivePartInfo
    {
        /// <summary>
        /// 认款单id
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditCode { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string? CompanyId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerId { get; set; }

        /// <summary>
        /// 业务单元id
        /// </summary>
        public string? ServiceId { get; set; }
    }
    /// <summary>
    /// 根据认款单号或销售单号查询认款单信息输出
    /// </summary>
    public class RecognizeReceiveBatchQueryByCodeOutput
    {
        /// <summary>
        /// 销售单号
        /// </summary>
        public string? SaleCode { get; set; }

        /// <summary>
        /// 认款单号
        /// </summary>
        public string? Code { get; set; }
        /// <summary>
        /// 收款单号
        /// </summary>
        public string? ReceiveCode { get; set; }
        /// <summary>
        /// 认款金额
        /// </summary>
        public decimal? ReceiveAmount { get; set; }
        /// <summary>
        /// 认款日期
        /// </summary>
        public DateTime? ReceiveDate { get; set; }

        /// <summary>
        /// 认款人
        /// </summary>
        public string? Receiver { get; set; }

        /// <summary>
        /// 认款类型
        /// </summary>
        public int Type { get; set; }
        /// <summary>
        /// 认款类型
        /// </summary>
        public string? TypeDescription { get { return ((RecognizeTypeEnums)this.Type).GetDescription(); } }
        /// <summary>
        /// 单据状态
        /// </summary>
        public RecognizeReceiveItemStatusEnum Status { get; set; }
        /// <summary>
        /// 单据状态说明
        /// </summary>
        public string? StatusDescription { get { return Status.GetDescription(); } }
        /// <summary>
        /// 明细状态
        /// </summary>
        public RecognizeReceiveDetailEnum? DetailStatus { get; set; }
        /// <summary>
        /// 明细状态说明
        /// </summary>
        public string? DetailStatusDescription
        {
            get
            {
                return DetailStatus?.GetDescription();
            }
        }
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditCode { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }
        /// <summary>
        /// 本次认款金额
        /// </summary>
        public decimal? CurrentValue { get; set; }
    }

    public class AgentRefundAbatementOutput
    {
        /// <summary>
        /// 应付单号
        /// </summary>
        public string DebtCode { get; set; }

        /// <summary>
        /// 收款单号
        /// </summary>
        public string ReceiveCode { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal AbatementValue { get; set; }

        /// <summary>
        /// 收款金额
        /// </summary>
        public decimal ReceiveValue { get; set; }

        /// <summary>
        /// 收款日期
        /// </summary>
        public string ReceiveDate { get; set; }

        /// <summary>
        /// 核算部门
        /// </summary>
        public string BusinessDeptFullName { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public string AgentName { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// 冲销人
        /// </summary>
        public string AbatementCreatedBy { get; set; }

        /// <summary>
        /// 冲销日期
        /// </summary>
        public string AbatementDate { get; set; }
    }


    public class AgentRefundAbatementInput : BaseQuery
    {
        /// <summary>
        /// 应付单号
        /// </summary>
        public string? DebtCode { get; set; }

        /// <summary>
        /// 收款单号
        /// </summary>
        public string? ReceiveCode { get; set; }

        /// <summary>
        /// 核算部门
        /// </summary>
        public string? Department { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 项目单号
        /// </summary>
        public string? ProjectNo { get; set; }

    }
}
