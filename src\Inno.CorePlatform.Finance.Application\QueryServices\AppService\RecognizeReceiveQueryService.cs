using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.DTOs.SPD;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Mapster;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver.Linq;
using System.Linq.Expressions;


namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    public class RecognizeReceiveQueryService : QueryAppService, IRecognizeReceiveQueryService
    {
        /// <summary>
        /// 注入数据库查询
        /// </summary>
        private readonly FinanceDbContext _db;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        protected readonly IPCApiClient _pcApiClient;
        private readonly IBDSApiClient _bDSApiClient;
        protected readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly ISellApiClient _sellApiClient;
        private readonly IPurchaseExcuteApiClient _purchaseExcuteApiClient;
        private object strategys;
        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="contextAccessor"></param>
        /// <param name="db"></param>
        public RecognizeReceiveQueryService(IAppServiceContextAccessor? contextAccessor,
            FinanceDbContext db,
            IKingdeeApiClient kingdeeApiClient,
            IPCApiClient pcApiClient,
            ISellApiClient sellApiClient,
            IPurchaseExcuteApiClient purchaseExcuteApiClient,
            IBDSApiClient bDSApiClient) : base(contextAccessor)
        {
            _db = db;
            _kingdeeApiClient = kingdeeApiClient;
            _pcApiClient = pcApiClient;
            _bDSApiClient = bDSApiClient;
            _sellApiClient = sellApiClient;
            _purchaseExcuteApiClient = purchaseExcuteApiClient;
            this._appServiceContextAccessor = contextAccessor;
        }
        /// <summary>
        /// 获取认款清单
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<RecognizeReceiveItemOutPut>> GetListPages(RecognizeReceiveItemInput input)
        {
            try
            {
                StrategyQueryOutput strategry = null;
                if (input.UserId.HasValue)
                {
                    var strategryq = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
                    strategry = await _pcApiClient.GetStrategyAsync(strategryq);
                    if (strategry != null)
                    {
                        var rowStrategies = strategry.RowStrategies;
                        if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company"))
                        {
                            return new PageResponse<RecognizeReceiveItemOutPut>
                            {
                                List = new List<RecognizeReceiveItemOutPut>(),
                                Total = 0
                            };
                        }
                    }
                }
                var query = _db.RecognizeReceiveItems.AsNoTracking();
                #region 条件
                if (!string.IsNullOrEmpty(input.Receivedetailcode))
                {
                    input.DetailCodes.Add(input.Receivedetailcode);
                }
                if (input.DetailCodes != null && input.DetailCodes.Any())
                {
                    var itemIds = await _db.RecognizeReceiveDetails.Where(p => input.DetailCodes.Contains(p.Code)).Select(p => p.RecognizeReceiveItemId).ToListAsync();
                    if (itemIds != null && itemIds.Any())
                    {
                        query = query.Where(p => itemIds.ToHashSet().Contains(p.Id));
                    }
                    else
                    {
                        query = query.Where(p => 1 != 1);
                    }
                }
                else if (input.DetailType.HasValue)
                {
                    var itemIds = await _db.RecognizeReceiveDetails.Where(p => p.Type == input.DetailType).Select(p => p.RecognizeReceiveItemId).ToListAsync();
                    if (itemIds != null && itemIds.Any())
                    {
                        query = query.Where(p => itemIds.ToHashSet().Contains(p.Id));
                    }
                    else
                    {
                        query = query.Where(p => 1 != 1);
                    }
                }
                if (!string.IsNullOrEmpty(input.CustomerName))
                {
                    var itemIds = await _db.RecognizeReceiveDetails.Where(p => p.CustomerNme.Contains(input.CustomerName)).Select(p => p.RecognizeReceiveItemId).ToListAsync();
                    if (itemIds != null && itemIds.Any())
                    {
                        query = query.Where(p => itemIds.ToHashSet().Contains(p.Id));
                    }
                    else
                    {
                        query = query.Where(p => 1 != 1);
                    }
                }
                if (!string.IsNullOrEmpty(input.HospitalName))
                {
                    var itemIds = await _db.RecognizeReceiveDetails.Where(p => p.HospitalName.Contains(input.HospitalName)).Select(p => p.RecognizeReceiveItemId).ToListAsync();
                    if (itemIds != null && itemIds.Any())
                    {
                        query = query.Where(p => itemIds.ToHashSet().Contains(p.Id));
                    }
                    else
                    {
                        query = query.Where(p => 1 != 1);
                    }
                }
                if (!string.IsNullOrEmpty(input.Classify) && input.Classify != "全部")
                {
                    query = query.Where(p => p.Type == input.Classify);
                }
                if (input.ItemClassify.HasValue)
                {
                    query = query.Where(z => input.ItemClassify.Equals(z.Classify));
                }
                if (!string.IsNullOrEmpty(input.searchKey))
                {
                    query = query.Where(p => EF.Functions.Like(p.Code, $"%{input.searchKey}%") ||
                                             EF.Functions.Like(p.ReceiveCode, $"%{input.searchKey}%"));
                }
                if (!input.Source.HasValue)
                {
                    if (input.customers != null && input.customers.Any())
                    {
                        query = query.Where(z => input.customers.ToHashSet().Contains(z.CustomerId));
                    }
                    if (input.CustomerId.HasValue)
                    {
                        query = query.Where(p => p.CustomerId.ToUpper() == input.CustomerId.Value.ToString().ToUpper());
                    }
                }
                if (strategry != null && strategry.RowStrategies.Any())
                {
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key.ToLower() == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToHashSet();
                                query = query.Where(t => strategList.Contains(t.CompanyId));
                            }
                        }
                        if (key.ToLower() == "accountingdept")
                        {
                            if (!strategry.RowStrategies[key].Any(s => s == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToHashSet();
                                query = query.Where(z => z.BusinessDepId != null && strategList.Contains(z.BusinessDepId.ToString()));
                            }
                        }
                        if (key.ToLower() == "customer")
                        {
                            if (!strategry.RowStrategies[key].Any(s => s == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToHashSet();
                                query = query.Where(z => strategList.Contains(z.CustomerId));
                            }
                        }
                    }
                }

                if (input.CompanyId.HasValue)
                {
                    query = query.Where(p => p.CompanyId.ToUpper() == input.CompanyId.Value.ToString().ToUpper());
                }
                if (!string.IsNullOrEmpty(input.department))
                {
                    query = query.Where(p => EF.Functions.Like(p.BusinessDeptFullPath, $"%{input.department}%"));
                }
                if (!string.IsNullOrEmpty(input.Code))
                {
                    query = query.Where(p => EF.Functions.Like(p.Code, $"%{input.Code}%"));
                }
                if (!string.IsNullOrEmpty(input.ProjectName))
                {
                    query = query.Where(p => EF.Functions.Like(p.ProjectName, $"%{input.ProjectName}%"));
                }
                if (!string.IsNullOrEmpty(input.ProjectCode))
                {
                    query = query.Where(p => p.ProjectCode == input.ProjectCode);
                }
                if (!string.IsNullOrEmpty(input.Receivecode))
                {
                    query = query.Where(p => EF.Functions.Like(p.ReceiveCode, $"%{input.Receivecode}%"));
                }
                if (input.BillDateStart.HasValue && input.BillDateEnd.HasValue)
                {
                    query = query.Where(p => p.BillDate >= input.BillDateStart.Value && p.BillDate <= input.BillDateEnd);
                }
                //收款日期
                if (input.ReceiveDateStart.HasValue && input.ReceiveDateEnd.HasValue)
                {
                    query = query.Where(p => p.ReceiveDate >= input.ReceiveDateStart.Value && p.ReceiveDate <= input.ReceiveDateEnd);
                }
                if (input.CreatedBy != null && input.CreatedBy.Any())
                {
                    query = query.Where(p => input.CreatedBy.ToHashSet().Contains(p.CreatedBy));
                }
                if (input.Status.HasValue)
                {
                    if (input.Status == (int)RecognizeReceiveItemStatusEnum.WaitSubmit)
                    {
                        query = query.Where(p => p.Status == (RecognizeReceiveItemStatusEnum)input.Status.Value && p.CreatedBy == input.Username);
                    }
                    else
                    {
                        query = query.Where(p => p.Status == (RecognizeReceiveItemStatusEnum)input.Status.Value);
                    }
                }
                if (!string.IsNullOrEmpty(input.Settletype))
                {
                    query = query.Where(p => p.Settletype == input.Settletype);
                }
                if (input.CreateDateStart.HasValue && input.CreateDateEnd.HasValue)
                {
                    query = query.Where(p => p.CreatedTime >= input.CreateDateStart.Value && p.CreatedTime <= input.CreateDateEnd);
                }

                // 转货款状态过滤
                if (input.TransferStatus.HasValue)
                {
                    if (input.TransferStatus.Value == 1) // 已转货款
                    {
                        query = query.Where(p => p.Classify == RecognizeReceiveClassifyEnum.Temp && !string.IsNullOrEmpty(p.RelateCode));
                    }
                    else // 未转货款
                    {
                        query = query.Where(p => p.Classify == RecognizeReceiveClassifyEnum.Temp && string.IsNullOrEmpty(p.RelateCode));
                    }
                }
                #endregion

                var queryLst = query.Select(z => new RecognizeReceiveItemOutPut
                {
                    BillDate = z.BillDate,
                    Code = z.Code,
                    Id = z.Id,
                    Value = z.Classify == RecognizeReceiveClassifyEnum.Goods ? z.RecognizeReceiveDetails.Where(x => x.Status != RecognizeReceiveDetailEnum.Cancel).Sum(p => p.Value) : z.RecognizeReceiveTempDetails.Where(x => x.Status != RecognizeReceiveDetailEnum.Cancel).Sum(p => p.Value - (p.CancelValue.HasValue ? p.CancelValue.Value : 0)),
                    Type = z.Type,
                    ReceiveCode = z.ReceiveCode,
                    ReceiveDate = z.ReceiveDate,
                    AttachFileIds = z.AttachFileIds,
                    BusinessDepId = z.BusinessDepId,
                    BusinessDeptFullName = z.BusinessDeptFullName,
                    BusinessDeptFullPath = z.BusinessDeptFullPath,
                    CompanyId = z.CompanyId,
                    CompanyName = z.CompanyName,
                    ProjectName = z.ProjectName,//项目名称
                    ProjectCode = z.ProjectCode,//项目编码
                    CustomerId = z.CustomerId,
                    CustomerNme = z.CustomerNme,
                    ReceiveValue = z.ReceiveValue,
                    Status = z.Status,
                    CreatedTime = z.CreatedTime,
                    CreatedBy = z.CreatedBy,
                    Classify = z.Classify,
                    RelateCode = z.RelateCode,
                    ActualCustomerIds = z.RecognizeReceiveDetails.Select(p => p.CustomerId),
                    Settletype = z.Settletype,//结算方式
                    DraftBillExpireDate = z.DraftBillExpireDate,//到期日
                    BankName = z.BankName,
                    BankNum = z.BankNum,
                    DiscountDate = z.DiscountDate,
                    BizTime = z.BizTime, //交易时间
                    RemainingRecognizableAmount = z.RemainingAmount ?? 0, //剩余认款金额（使用数据库冗余字段）
                });
                var count = await queryLst.CountAsync();
                //分页
                var list = await queryLst.OrderByDefault(input.sort).Skip((input.page - 1) * input.limit).Take(input.limit).ToListAsync();

                // 计算转货款状态
                foreach (var item in list)
                {
                    if (item.Classify == RecognizeReceiveClassifyEnum.Temp)
                    {
                        // 暂收款：有RelateCode就是已转货款，否则未转货款
                        item.TransferStatus = !string.IsNullOrEmpty(item.RelateCode) ? 1 : 0;
                    }
                    else
                    {
                        // 货款类型的转货款状态始终为0（未转货款）
                        item.TransferStatus = 0;
                    }
                }

                return new PageResponse<RecognizeReceiveItemOutPut>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        /// <summary>
        /// 获取认款清单（页签数量）
        /// </summary>
        /// <returns></returns>
        public async Task<BaseResponseData<RecognizeReceiveItemTabCount>> GetTabCount(RecognizeReceiveItemInput input)
        {
            var ret = BaseResponseData<RecognizeReceiveItemTabCount>.Success("获取成功");
            try
            {
                StrategyQueryOutput strategry = null;
                if (input.UserId.HasValue)
                {
                    var strategryq = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
                    strategry = await _pcApiClient.GetStrategyAsync(strategryq);
                    if (strategry != null)
                    {
                        var rowStrategies = strategry.RowStrategies;
                        if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company"))
                        {
                            ret.Data = new RecognizeReceiveItemTabCount
                            {
                                allCount = 0,
                                completedCount = 0,
                                auditingCount = 0,
                                waitExecuteCount = 0,
                                waitSubmitCount = 0,
                                partCancel = 0
                            };
                            ret.Message = "没有部门和公司权限";
                            return ret;
                        }
                    }
                }
                var query = _db.RecognizeReceiveItems.AsNoTracking();
                #region 条件
                if (!string.IsNullOrEmpty(input.Receivedetailcode))
                {
                    input.DetailCodes.Add(input.Receivedetailcode);
                }
                if (input.DetailCodes != null && input.DetailCodes.Any())
                {
                    var itemIds = await _db.RecognizeReceiveDetails.Where(p => input.DetailCodes.Contains(p.Code)).Select(p => p.RecognizeReceiveItemId).ToListAsync();
                    query = query.Where(p => itemIds.ToHashSet().Contains(p.Id));
                }
                else if (input.DetailType.HasValue)
                {
                    var itemIds = await _db.RecognizeReceiveDetails.Where(p => p.Type == input.DetailType).Select(p => p.RecognizeReceiveItemId).ToListAsync();
                    query = query.Where(p => itemIds.ToHashSet().Contains(p.Id));
                }
                if (!string.IsNullOrEmpty(input.CustomerName))
                {
                    var itemIds = await _db.RecognizeReceiveDetails.Where(p => p.CustomerNme.Contains(input.CustomerName)).Select(p => p.RecognizeReceiveItemId).ToListAsync();
                    if (itemIds != null && itemIds.Any())
                    {
                        query = query.Where(p => itemIds.ToHashSet().Contains(p.Id));
                    }
                    else
                    {
                        query = query.Where(p => 1 != 1);
                    }
                }
                if (!string.IsNullOrEmpty(input.HospitalName))
                {
                    var itemIds = await _db.RecognizeReceiveDetails.Where(p => p.HospitalName.Contains(input.HospitalName)).Select(p => p.RecognizeReceiveItemId).ToListAsync();
                    if (itemIds != null && itemIds.Any())
                    {
                        query = query.Where(p => itemIds.ToHashSet().Contains(p.Id));
                    }
                    else
                    {
                        query = query.Where(p => 1 != 1);
                    }
                }
                if (!string.IsNullOrEmpty(input.Classify) && input.Classify != "全部")
                {
                    query = query.Where(p => p.Type == input.Classify);
                }
                if (input.ItemClassify.HasValue)
                {
                    query = query.Where(z => input.ItemClassify.Equals(z.Classify));
                }
                if (!string.IsNullOrEmpty(input.searchKey))
                {
                    query = query.Where(p => EF.Functions.Like(p.Code, $"%{input.searchKey}%") ||
                                             EF.Functions.Like(p.ReceiveCode, $"%{input.searchKey}%"));
                }
                if (!input.Source.HasValue)
                {
                    if (input.customers != null && input.customers.Any())
                    {
                        query = query.Where(z => input.customers.ToHashSet().Contains(z.CustomerId));
                    }
                    if (input.CustomerId.HasValue)
                    {
                        query = query.Where(p => p.CustomerId.ToUpper() == input.CustomerId.Value.ToString().ToUpper());
                    }
                }
                if (strategry != null && strategry.RowStrategies.Any())
                {
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key.ToLower() == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToHashSet();
                                query = query.Where(t => strategList.Contains(t.CompanyId));
                            }
                        }
                        if (key.ToLower() == "accountingdept")
                        {
                            if (!strategry.RowStrategies[key].Any(s => s == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToHashSet();
                                query = query.Where(z => z.BusinessDepId != null && strategList.Contains(z.BusinessDepId.ToString()));
                            }
                        }
                        if (key.ToLower() == "customer")
                        {
                            if (!strategry.RowStrategies[key].Any(s => s == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToHashSet();
                                query = query.Where(z => strategList.Contains(z.CustomerId));
                            }
                        }
                    }
                }

                if (input.CompanyId.HasValue)
                {
                    query = query.Where(p => p.CompanyId.ToUpper() == input.CompanyId.Value.ToString().ToUpper());
                }
                if (!string.IsNullOrEmpty(input.department))
                {
                    query = query.Where(p => EF.Functions.Like(p.BusinessDeptFullPath, $"%{input.department}%"));
                }
                if (!string.IsNullOrEmpty(input.Code))
                {
                    query = query.Where(p => EF.Functions.Like(p.Code, $"%{input.Code}%"));
                }
                if (!string.IsNullOrEmpty(input.ProjectName))
                {
                    query = query.Where(p => EF.Functions.Like(p.ProjectName, $"%{input.ProjectName}%"));
                }
                if (!string.IsNullOrEmpty(input.ProjectCode))
                {
                    query = query.Where(p => p.ProjectCode == input.ProjectCode);
                }
                if (!string.IsNullOrEmpty(input.Receivecode))
                {
                    query = query.Where(p => EF.Functions.Like(p.ReceiveCode, $"%{input.Receivecode}%"));
                }
                if (input.BillDateStart.HasValue && input.BillDateEnd.HasValue)
                {
                    query = query.Where(p => p.BillDate >= input.BillDateStart.Value && p.BillDate <= input.BillDateEnd);
                }
                //收款日期
                if (input.ReceiveDateStart.HasValue && input.ReceiveDateEnd.HasValue)
                {
                    query = query.Where(p => p.ReceiveDate >= input.ReceiveDateStart.Value && p.ReceiveDate <= input.ReceiveDateEnd);
                }
                if (input.CreatedBy != null && input.CreatedBy.Any())
                {
                    query = query.Where(p => input.CreatedBy.ToHashSet().Contains(p.CreatedBy));
                }
                if (!string.IsNullOrEmpty(input.Settletype))
                {
                    query = query.Where(p => p.Settletype == input.Settletype);
                }
                if (input.CreateDateStart.HasValue && input.CreateDateEnd.HasValue)
                {
                    query = query.Where(p => p.CreatedTime >= input.CreateDateStart.Value && p.CreatedTime <= input.CreateDateEnd);
                }
                #endregion
                var list = await query.ToListAsync();
                ret.Data = new RecognizeReceiveItemTabCount
                {
                    allCount = list.Count(),
                    completedCount = list.Where(t => t.Status == RecognizeReceiveItemStatusEnum.Completed).Count(),
                    auditingCount = list.Where(t => t.Status == RecognizeReceiveItemStatusEnum.Canceled).Count(),
                    waitExecuteCount = list.Where(t => t.Status == RecognizeReceiveItemStatusEnum.Auditing).Count(),
                    waitSubmitCount = list.Where(t => t.Status == RecognizeReceiveItemStatusEnum.WaitSubmit && t.CreatedBy == input.Username).Count(),
                    partCancel = list.Where(t => t.Status == RecognizeReceiveItemStatusEnum.PartCanceled).Count()
                };
                return ret;
            }
            catch (Exception ex)
            {
                ret.Data = new RecognizeReceiveItemTabCount
                {
                    allCount = 0,
                    completedCount = 0,
                    auditingCount = 0,
                    waitExecuteCount = 0,
                    waitSubmitCount = 0,
                    partCancel = 0
                };
                ret.Message = ex.Message;
                return ret;
            }
        }

        /// <summary>
        /// 异步获取可认款金额（根据单号）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<RemainingRecognizableAmountOutput>>> GetRemainingRecognizableAmountByCodes(List<string> codes)
        {
            var ret = BaseResponseData<List<RemainingRecognizableAmountOutput>>.Success("获取成功");
            try
            {
                var list = await _db.RecognizeReceiveItems.Where(x => codes.ToHashSet().Contains(x.Code)).AsNoTracking().ToListAsync();
                // 计算剩余可认款金额
                var receiveCodes = list.Select(x => x.ReceiveCode).Distinct().ToList();
                if (receiveCodes.Any())
                {
                    // .Include(x=>x.RecognizeReceiveDetails)
                    var receives = await _db.RecognizeReceiveItems
                        .Include(x => x.RecognizeReceiveDetails)
                        .Include(x => x.RecognizeReceiveTempDetails)
                        .Where(x => receiveCodes.ToHashSet().Contains(x.ReceiveCode) && x.Status != RecognizeReceiveItemStatusEnum.Canceled).AsNoTracking().ToListAsync();
                    var refunds = await _db.RefundItem.Where(x => x.Status != RefundStatusEnum.Refuse && receiveCodes.ToHashSet().Contains(x.ReceivablesNumber)).ToListAsync();
                    var refundDetails = await _db.RefundDetails.Include(x => x.RefundItem).Where(x => x.RefundItem.Status != RefundStatusEnum.Refuse && receiveCodes.ToHashSet().Contains(x.MinusNumber)).ToListAsync();
                    var data = new List<RemainingRecognizableAmountOutput>();
                    foreach (var item in list)
                    {
                        var meValue = 0M;
                        if (item.Status == RecognizeReceiveItemStatusEnum.WaitSubmit)
                        {
                            meValue = receives.Where(x => x.Code == item.Code).Sum(x => x.RecognizeReceiveDetails.Sum(d => d.Value));
                        }
                        var single = new RemainingRecognizableAmountOutput();
                        single.Code = item.Code;
                        var currentreceives = receives.Where(x => x.ReceiveCode == item.ReceiveCode).ToList();
                        decimal value = 0;
                        if (item.Classify == RecognizeReceiveClassifyEnum.Goods)
                        {
                            // 由暂收款转货款的认款单，剩余可认款金额=收款单金额-本次认款金额
                            if (!string.IsNullOrEmpty(item.RelateCode))
                            {
                                value = currentreceives.Where(x => x.CustomerId == item.CustomerId && x.RelateCode == item.RelateCode).Sum(x => x.RecognizeReceiveDetails.Where(x => x.Status != RecognizeReceiveDetailEnum.Cancel).Sum(d => d.Value));
                            }
                            else
                            {
                                value = currentreceives.Where(x => x.CompanyId == item.CompanyId).Sum(x => x.RecognizeReceiveDetails.Where(x => x.Status != RecognizeReceiveDetailEnum.Cancel).Sum(d => d.Value));
                            }
                        }
                        else
                        {
                            value = currentreceives.Sum(x => x.RecognizeReceiveTempDetails.Where(x => x.Status != RecognizeReceiveDetailEnum.Cancel).Sum(d => d.Value - (d.CancelValue.HasValue ? d.CancelValue.Value : 0)));
                        }
                        //value -= meValue;
                        //获取退款金额
                        decimal? refundValue = 0M;
                        if (item.ReceiveValue > 0)
                        {
                            refundValue = refunds.Where(x => x.ReceivablesNumber == item.ReceiveCode).Sum(x => x.RefundAllMoney);
                        }
                        else
                        {
                            refundValue = 0 - refundDetails.Where(x => x.MinusNumber == item.ReceiveCode).Sum(x => x.RefundMoney);
                        }
                        single.RemainingRecognizableAmount = item.ReceiveValue < 0 ? item.ReceiveValue + value - refundValue : item.ReceiveValue - value - refundValue;
                        if (item.ReceiveValue >= 0)
                        {
                            single.RemainingRecognizableAmount = single.RemainingRecognizableAmount < 0 ? 0 : single.RemainingRecognizableAmount;
                        }
                        data.Add(single);
                    }
                    ret.Data = data;
                }
                return ret;
            }
            catch (Exception ex)
            {
                ret.Message = ex.Message;
                return ret;
            }
        }

        public async Task<RecognizeReceiveItemOutPut> GetItemById(Guid id)
        {
            var ret = await _db.RecognizeReceiveItems.FirstOrDefaultAsync(p => p.Id == id);
            return ret.Adapt<RecognizeReceiveItemOutPut>();
        }
        public async Task<List<RecognizeReceiveItemOutPut>> GetItemByReceiveCode(string receiveCode)
        {
            var ret = await _db.RecognizeReceiveItems.Where(p => p.ReceiveCode == receiveCode).ToListAsync();
            return ret.Adapt<List<RecognizeReceiveItemOutPut>>();
        }
        /// <summary>
        /// 获取认款款详情
        /// </summary>   
        /// <param name="id"></param
        /// <param name="Classify"></param>
        /// <returns></returns>
        public async Task<List<RecognizeReceiveDetailOutput>> GetDetails(Guid? id, int? Classify, RecognizeReceiveDetailEnum? status)
        {
            try
            {
                if (Classify == (int)RecognizeReceiveClassifyEnum.Goods)
                {
                    Expression<Func<RecognizeReceiveDetailPo, bool>> exp = t => t.RecognizeReceiveItemId == id;
                    if (status.HasValue)
                    {
                        exp = exp.And(x => x.Status == status);
                    }
                    var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
                    {
                        Names = new List<string> { _appServiceContextAccessor.Get().UserName }
                    });
                    var input = new StrategyQueryInput() { userId = _appServiceContextAccessor.Get().UserId, functionUri = "metadata://fam" };
                    var strategry = await _pcApiClient.GetStrategyAsync(input);
                    if (strategry != null && strategry.RowStrategies.Any())
                    {
                        foreach (var key in strategry.RowStrategies.Keys)
                        {
                            if (key.ToLower() == "customer")
                            {
                                if (!strategry.RowStrategies[key].Any(s => s == "@all"))
                                {
                                    var strategList = strategry.RowStrategies[key].ToHashSet();
                                    exp = exp.And(z => string.IsNullOrEmpty(z.CustomerId) || strategList.Contains(z.CustomerId));
                                }
                            }
                        }
                    }

                    var query = _db.RecognizeReceiveDetails.Where(exp).Select(t => t.Adapt<RecognizeReceiveDetailOutput>()).AsNoTracking();
                    var listAll = await query.ToListAsync();
                    var list = new List<RecognizeReceiveDetailOutput>();
                    var partlist = new List<RecognizeReceiveDetailOutput>();
                    var dictionaryOutputs = new List<DataDictionaryOutput>();
                    var dictionaryNames = new List<string>();
                    var ilist = new List<InvoicePo>();
                    var icodes = listAll.Where(x => x.Type == 1).Select(x => x.Code).ToList();
                    if (icodes != null && icodes.Any())
                    {
                        ilist = await (from i in _db.Invoices where !string.IsNullOrEmpty(i.InvoiceNo) && icodes.ToHashSet().Contains(i.InvoiceNo) select i).ToListAsync();
                    }
                    if (user.Data.List.First().InstitutionType == 4)
                    {
                        //校验发票
                        if (user.Data.List.First().Institutions.Any())
                        {
                            dictionaryOutputs = await _bDSApiClient.GetDataDictionaryListByType("NoDisplayPriceForTG");
                            dictionaryNames = dictionaryOutputs.Select(x => x.DictionaryName.ToUpper()).ToList();
                            // 拆分（业务单元_公司_客户）
                            var newStrs = new HashSet<string>();
                            foreach (var dic in dictionaryNames)
                            {
                                string[] result = dic.Split("_");
                                if (result.Count() >= 3)
                                {
                                    var serviceId = result[0].ToUpper();
                                    var companyId = result[1].ToUpper();
                                    var customerId = result[2].ToUpper();
                                    // 需要作为判断的新字符串（去掉业务单元）
                                    var newStr = string.Concat(serviceId, "_", companyId, "_", customerId);
                                    newStrs.Add(newStr);
                                }
                            }
                            var codes = listAll.Where(x => x.Type == 1 || x.Type == 3).Select(x => x.Code).ToList();
                            if (codes == null || !codes.Any())
                            {
                                return list;
                            }
                            #region 发票
                            if (icodes != null && icodes.Any())
                            {
                                // 1.终端医院为空直接展示，终端医院等于客户显示
                                var ids = new List<string>();
                                var partIds1 = ilist.Where(x => string.IsNullOrEmpty(x.HospitalName) || x.CustomerName == x.HospitalName).Select(x => x.InvoiceNo).ToList();
                                ids.AddRange(partIds1);
                                // 2.托管公司配置里存在的不显示
                                var invoiceNos = ilist.Where(x => !string.IsNullOrEmpty(x.HospitalName) && x.CustomerName != x.HospitalName).Select(x => x.InvoiceNo).ToHashSet();
                                if (invoiceNos != null && invoiceNos.Any() && newStrs.Any())
                                {
                                    var restrictedInvoiceNos = await (from ic in _db.InvoiceCredits
                                                                      join c in _db.Credits on ic.CreditId equals c.Id into icGroup
                                                                      from c in icGroup.DefaultIfEmpty()
                                                                      where
                                                                      c.ServiceId.HasValue && c.CompanyId.HasValue &&
                                                                      c.CustomerId.HasValue &&
                                                                      invoiceNos.ToHashSet().Contains(ic.InvoiceNo) &&
                                                                      newStrs.ToHashSet().Contains(c.ServiceId.Value.ToString().ToUpper() + "_" + c.CompanyId.Value.ToString().ToUpper() + "_" + c.CustomerId.Value.ToString().ToUpper())
                                                                      select ic.InvoiceNo).ToListAsync();
                                    if (restrictedInvoiceNos != null && restrictedInvoiceNos.Any())
                                    {
                                        list.AddRange(listAll.Where(x => !restrictedInvoiceNos.ToHashSet().Contains(x.Code)).ToList());
                                    }
                                }
                                else
                                {
                                    list.AddRange(listAll);
                                }
                            }
                            #endregion

                            #region 初始应收
                            var ccodes = listAll.Where(x => x.Type == 3).Select(x => x.Code).ToList();
                            if (ccodes != null && ccodes.Any())
                            {
                                var clist = await _db.Credits.Where(x => ccodes.ToHashSet().Contains(x.BillCode) && x.CreditType == CreditTypeEnum.origin).AsNoTracking().ToListAsync();
                                // 1.终端医院为空直接展示，终端医院等于客户显示
                                var cids = new List<string>();
                                var partIds3 = clist.Where(x => string.IsNullOrEmpty(x.HospitalName) || x.CustomerName == x.HospitalName).Select(x => x.BillCode).ToList();
                                cids.AddRange(partIds3);
                                // 2.托管公司配置里存在的不显示
                                var partIds4 = clist.Where(x => !string.IsNullOrEmpty(x.HospitalName) && x.CustomerName != x.HospitalName && x.ServiceId.HasValue && x.CompanyId.HasValue && x.CustomerId.HasValue && !dictionaryNames.Contains(x.ServiceId.Value.ToString().ToUpper() + "_" + x.CompanyId.Value.ToString().ToUpper() + "_" + x.CustomerId.Value.ToString().ToUpper())).Select(x => x.BillCode).ToList();
                                cids.AddRange(partIds4);
                                if (cids.Any())
                                {
                                    list.AddRange(listAll.Where(x => cids.ToHashSet().Contains(x.Code)).ToList());
                                }
                            }
                            #endregion
                        }
                    }
                    else
                    {
                        list = listAll;
                    }
                    if (list.Any())
                    {
                        //获取认款明细应收
                        var detailIds = list.Select(x => x.Id).ToList();
                        var rrdcs = await _db.RecognizeReceiveDetailCredits.Where(x => detailIds.Any(p => p == x.RecognizeReceiveDetailId)).Include(x => x.Credit).AsNoTracking().ToListAsync();
                        var creditCodes = rrdcs.Select(x => x.CreditCode).ToList();
                        var credits = await _db.Credits.Where(x => creditCodes != null && creditCodes.Any() && creditCodes.ToHashSet().Contains(x.BillCode)).AsNoTracking().ToListAsync();
                        if (user.Data.List.First().InstitutionType == 4)
                        {
                            // 应收
                            // 1.终端医院为空直接展示，终端医院等于客户显示
                            var ids = new List<string>();
                            var partIds1 = credits.Where(x => string.IsNullOrEmpty(x.HospitalName) || x.CustomerName == x.HospitalName).Select(x => x.BillCode).ToList();
                            ids.AddRange(partIds1);
                            // 2.托管公司配置里存在的不显示
                            var partIds2 = credits.Where(x => !string.IsNullOrEmpty(x.HospitalName) && x.CustomerName != x.HospitalName && x.ServiceId.HasValue && x.CompanyId.HasValue && x.CustomerId.HasValue && !dictionaryNames.Contains(x.ServiceId.Value.ToString().ToUpper() + "_" + x.CompanyId.Value.ToString().ToUpper() + "_" + x.CustomerId.Value.ToString().ToUpper())).Select(x => x.BillCode).ToList();
                            ids.AddRange(partIds2);
                            var idsHashSet = ids.ToHashSet();
                            if (ids.Any())
                            {
                                rrdcs = rrdcs.Where(x => idsHashSet.Contains(x.CreditCode)).ToList();
                            }
                            else
                            {
                                rrdcs = rrdcs.Where(x => 1 != 1).ToList();
                            }
                        }
                        var first = list.FirstOrDefault();
                        if (first.Type == (int)RecognizeTypeEnums.Invoice)
                        {
                            foreach (var item in list)
                            {
                                var creditInfos = new List<DTOs.Recognize.CreditInfo>();
                                var currentrrdcs = rrdcs.Where(x => x.RecognizeReceiveDetailId == item.Id).ToList();
                                if (user.Data.List.First().InstitutionType == 4)
                                {
                                    //业务单元端根据业务单元过滤
                                    var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id);
                                    if (serviceIds != null)
                                    {
                                        var currentCreditCodes = credits.Where(x => serviceIds.ToHashSet().Contains(x.ServiceId)).Select(x => x.BillCode).ToHashSet();
                                        currentrrdcs = currentrrdcs.Where(x => currentCreditCodes.Contains(x.CreditCode)).ToList();
                                    }
                                }
                                foreach (var rrcd in currentrrdcs)
                                {
                                    var creditInfo = new DTOs.Recognize.CreditInfo();
                                    creditInfo.BillCode = rrcd.CreditCode;
                                    creditInfo.BillDate = rrcd.Credit != null ? rrcd.Credit.BillDate : rrcd.CreatedTime.DateTime;
                                    creditInfo.CreditType = rrcd.Credit?.CreditType;
                                    creditInfo.OrderNo = rrcd.Credit?.OrderNo;
                                    creditInfo.ProjectCode = rrcd.Credit?.ProjectCode;
                                    creditInfo.ProjectId = rrcd.Credit?.ProjectId;
                                    creditInfo.ProjectName = rrcd.Credit?.ProjectName;
                                    creditInfo.ServiceId = rrcd.Credit?.ServiceId;
                                    creditInfo.ServiceName = rrcd.Credit?.ServiceName;
                                    creditInfo.Value = rrcd.Credit?.Value;
                                    creditInfo.CurrentValue = rrcd.CurrentValue;
                                    creditInfos.Add(creditInfo);
                                }
                                item.CreditInfo = creditInfos;
                                var single = ilist.FirstOrDefault(x => x.InvoiceNo == item.Code);
                                item.CodeTime = single != null ? single.InvoiceTime.Value.ToString("yyyy-MM-dd") : string.Empty;
                                item.InvoiceAmount = single != null ? single.InvoiceAmount : 0;
                            }
                        }
                        else if (first.Type == (int)RecognizeTypeEnums.Orderno)
                        {
                            var codes = list.Where(x => x.Type == 2).Select(x => x.Code).ToList();
                            var saleOut = await _sellApiClient.GetSaleList(new GetSaleListInput
                            {
                                BillCodes = codes,
                                PageSize = int.MaxValue,
                                PageNum = 1
                            });
                            foreach (var item in list)
                            {
                                var creditInfos = new List<DTOs.Recognize.CreditInfo>();
                                var currentrrdcs = rrdcs.Where(x => x.RecognizeReceiveDetailId == item.Id).ToList();
                                if (user.Data.List.First().InstitutionType == 4)
                                {
                                    //业务单元端根据业务单元过滤
                                    var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id);
                                    if (serviceIds != null)
                                    {
                                        var currentCreditCodes = credits.Where(x => serviceIds.ToHashSet().Contains(x.ServiceId)).Select(x => x.BillCode).ToHashSet();
                                        currentrrdcs = currentrrdcs.Where(x => currentCreditCodes.Contains(x.CreditCode)).ToList();
                                    }
                                }
                                foreach (var rrcd in currentrrdcs)
                                {
                                    var creditInfo = new DTOs.Recognize.CreditInfo();
                                    creditInfo.BillCode = rrcd.CreditCode;
                                    creditInfo.BillDate = rrcd.Credit != null ? rrcd.Credit.BillDate : rrcd.CreatedTime.DateTime;
                                    creditInfo.CreditType = rrcd.Credit?.CreditType;
                                    creditInfo.OrderNo = rrcd.Credit != null ? rrcd.OrderNo : item.Code;
                                    creditInfo.ProjectCode = rrcd.Credit?.ProjectCode;
                                    creditInfo.ProjectId = rrcd.Credit?.ProjectId;
                                    creditInfo.ProjectName = rrcd.Credit?.ProjectName;
                                    creditInfo.ServiceId = rrcd.Credit?.ServiceId;
                                    creditInfo.ServiceName = rrcd.Credit?.ServiceName;
                                    creditInfo.Value = rrcd.Credit?.Value;
                                    creditInfo.CurrentValue = rrcd.CurrentValue;
                                    creditInfos.Add(creditInfo);
                                }
                                item.CreditInfo = creditInfos;
                                var single = saleOut != null && saleOut.Any() ? saleOut.FirstOrDefault(x => x.BillCode == item.Code) : null;
                                item.CodeTime = single != null ? single.BillDate.ToString("yyyy-MM-dd") : string.Empty;
                            }
                        }
                        else if (first.Type == (int)RecognizeTypeEnums.Credit)
                        {
                            var codes = list.Where(x => x.Type == 3).Select(x => x.Code).ToList();
                            var clist = await (from c in _db.Credits where codes.Contains(c.BillCode) select c).ToListAsync();
                            foreach (var item in list)
                            {
                                var single = clist.FirstOrDefault(x => x.BillCode == item.Code);
                                item.CodeTime = single != null ? single.BillDate.Value.ToString("yyyy-MM-dd") : string.Empty;
                            }
                        }
                    }

                    return list.DistinctBy(p => p.Code).ToList();
                }
                else if (Classify == (int)RecognizeReceiveClassifyEnum.Temp)
                {
                    var item = await _db.RecognizeReceiveItems.FirstAsync(x => x.Id == id);
                    if (item == null)
                    {
                        return new List<RecognizeReceiveDetailOutput>();
                    }
                    var goods = await _db.RecognizeReceiveItems.Include(x => x.RecognizeReceiveDetails).Where(x => x.RelateCode == item.Code).AsNoTracking().ToListAsync();
                    var query = _db.RecognizeReceiveTempDetails.Where(t => t.RecognizeReceiveItemId == id).Select(t => t.Adapt<RecognizeReceiveDetailOutput>()).AsNoTracking();
                    var list = await query.ToListAsync();
                    foreach (var detail in list)
                    {
                        var single = goods.Where(x => x.CustomerId == detail.CustomerId);
                        if (single == null)
                        {
                            detail.SurplusValue = detail.Value - (detail.CancelValue.HasValue ? detail.CancelValue.Value : 0);
                            detail.CurrentValue = detail.SurplusValue;
                        }
                        else
                        {
                            detail.UseValue = single.Sum(item => item.RecognizeReceiveDetails
                                .Where(detail => detail.Status != RecognizeReceiveDetailEnum.Cancel)
                                .Sum(detail => detail.Value));
                            detail.SurplusValue = detail.Value - detail.UseValue - (detail.CancelValue.HasValue ? detail.CancelValue.Value : 0);
                            detail.CurrentValue = detail.SurplusValue;
                        }
                    }
                    return list;
                }
                else { return new List<RecognizeReceiveDetailOutput>(); }
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }
        /// <summary>
        /// 获取认款单详情
        /// </summary>
        /// <param name="ids">id</param>
        /// <param name="Classify">类型</param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<List<RecognizeReceiveDetailOutput>> GetDetailsByIdAndClassify(List<Guid> ids, int? Classify)
        {
            try
            {
                if (Classify == (int)RecognizeReceiveClassifyEnum.Goods)
                {
                    Expression<Func<RecognizeReceiveDetailPo, bool>> exp = t => ids.Contains(t.RecognizeReceiveItemId);

                    var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
                    {
                        Names = new List<string> { _appServiceContextAccessor.Get().UserName }
                    });
                    var input = new StrategyQueryInput() { userId = _appServiceContextAccessor.Get().UserId, functionUri = "metadata://fam" };
                    var strategry = await _pcApiClient.GetStrategyAsync(input);
                    if (strategry != null && strategry.RowStrategies.Any())
                    {
                        foreach (var key in strategry.RowStrategies.Keys)
                        {
                            if (key.ToLower() == "customer")
                            {
                                if (!strategry.RowStrategies[key].Any(s => s == "@all"))
                                {
                                    var strategList = strategry.RowStrategies[key].Select(s => s).ToHashSet();
                                    exp = exp.And(z => string.IsNullOrEmpty(z.HospitalId) || strategList.Contains(z.HospitalId));
                                }
                            }
                        }
                    }

                    var query = _db.RecognizeReceiveDetails.Where(exp).Select(t => t.Adapt<RecognizeReceiveDetailOutput>()).AsNoTracking();
                    var listAll = await query.ToListAsync();
                    var list = new List<RecognizeReceiveDetailOutput>();
                    if (user.Data.List.First().InstitutionType == 4)
                    {
                        if (user.Data.List.First().Institutions.Any())
                        {
                            var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id);
                            if (serviceIds != null)
                            {
                                list.AddRange(listAll.Where(t => serviceIds.Contains(t.ServiceId)));
                                var invoiceDetails = listAll.Where(p => p.Type == 1).ToList();
                                if (invoiceDetails != null)
                                {
                                    var invoiceNos = invoiceDetails.Select(p => p.Code).ToHashSet();
                                    var invoiceCredits = await _db.InvoiceCredits.Include(p => p.Credit).Where(p => invoiceNos.Contains(p.InvoiceNo)).ToListAsync();
                                    foreach (var detail in invoiceDetails)
                                    {
                                        var temp_serverIds = invoiceCredits.Where(p => p.InvoiceNo == detail.Code).Select(p => p.Credit?.ServiceId).ToList();
                                        if (temp_serverIds != null && serviceIds.Intersect(temp_serverIds).Count() > 0)
                                        {
                                            list.Add(detail);
                                        }
                                    }

                                }
                                // 业务单元缺省值 
                                //exp = exp.And(t => serviceIds.Contains(t.ServiceId));
                            }
                        }
                    }
                    else
                    {
                        list = listAll;
                    }
                    if (list.Any())
                    {
                        var codes = list.Select(x => x.Code).ToList();
                        var ilist = new List<InvoicePo>();
                        if (list.Select(x => x.Type).Contains((int)RecognizeTypeEnums.Invoice))
                        {
                            ilist = await (from i in _db.Invoices where codes.Contains(i.InvoiceNo) select i).ToListAsync();
                        }
                        var saleOut = new List<SaleOutput>();
                        if (list.Select(x => x.Type).Contains((int)RecognizeTypeEnums.Orderno))
                        {
                            saleOut = await _sellApiClient.GetSaleList(new GetSaleListInput
                            {
                                BillCodes = codes,
                                PageSize = int.MaxValue,
                                PageNum = 1
                            });
                        }
                        var clist = new List<CreditPo>();
                        if (list.Select(x => x.Type).Contains((int)RecognizeTypeEnums.Credit))
                        {
                            clist = await (from c in _db.Credits where codes.Contains(c.BillCode) select c).ToListAsync();
                        }
                        foreach (var item in list)
                        {
                            if (item.Type == (int)RecognizeTypeEnums.Invoice)
                            {
                                var single = ilist.FirstOrDefault(x => x.InvoiceNo == item.Code);
                                item.CodeTime = single != null ? single.InvoiceTime.Value.ToString("yyyy-MM-dd") : string.Empty;
                            }
                            else if (item.Type == (int)RecognizeTypeEnums.Orderno)
                            {
                                var single = saleOut.FirstOrDefault(x => x.BillCode == item.Code);
                                item.CodeTime = single != null ? single.BillDate.ToString("yyyy-MM-dd") : string.Empty;
                            }
                            else if (item.Type == (int)RecognizeTypeEnums.Credit)
                            {
                                var single = clist.FirstOrDefault(x => x.BillCode == item.Code);
                                item.CodeTime = single != null ? single.BillDate.Value.ToString("yyyy-MM-dd") : string.Empty;
                            }
                        }
                    }
                    return list.DistinctBy(p => new { p.Code, p.RecognizeReceiveItemId }).ToList();
                }
                else if (Classify == (int)RecognizeReceiveClassifyEnum.Temp)
                {
                    var query = _db.RecognizeReceiveTempDetails.Where(t => ids.Contains(t.RecognizeReceiveItemId)).Select(t => t.Adapt<RecognizeReceiveDetailOutput>()).AsNoTracking();
                    return await query.ToListAsync();
                }
                else { return new List<RecognizeReceiveDetailOutput>(); }
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }
        /// <summary>
        /// 获取认款款详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<RecognizeReceiveDetailOutput> GetDetail(Guid? id)
        {
            try
            {
                var query = _db.RecognizeReceiveDetails.Where(t => t.Id == id).Select(t => t.Adapt<RecognizeReceiveDetailOutput>()).AsNoTracking();
                var detail = await query.FirstOrDefaultAsync();
                if (detail == null)
                {
                    query = _db.RecognizeReceiveTempDetails.Where(t => t.Id == id).Select(t => t.Adapt<RecognizeReceiveDetailOutput>()).AsNoTracking();
                    detail = await query.FirstOrDefaultAsync();
                }
                return detail;
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }
        /// <summary>
        /// 获取认款款详情
        /// </summary>   
        /// <param name="ids"></param
        /// <param name="Classify"></param>
        /// <returns></returns>
        public async Task<List<RecognizeReceiveDetailOutput>> GetDetailByIds(List<Guid> ids)
        {
            try
            {
                var query = _db.RecognizeReceiveDetails.Where(t => ids.Contains(t.RecognizeReceiveItemId)).Select(t => t.Adapt<RecognizeReceiveDetailOutput>()).AsNoTracking();
                return await query.ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }
        public async Task<List<RecognizeReceiveOutput>> GetKdReceiveBills(RecognizeReceiveInput input)
        {
            try
            {
                var result = await _kingdeeApiClient.PullReceiveBills(input);
                if (result.Code == CorePlatform.Common.Http.CodeStatusEnum.Success)
                {
                    if (input.opt != 1)
                    {
                        return result.Data.Where(p => p.receivingtype != "负数应收").ToList();
                    }
                    return result.Data;
                }
                else
                {
                    throw new ApplicationException($"金蝶拉取收款单失败：{result.Message}");
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }
        /// <summary>
        /// 根据业务单元获取认款清单
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<RecognizeReceiveItemOutPut>> GetListByServiceIdPages(RecognizeReceiveItemInput input)
        {
            try
            {
                Expression<Func<RecognizeReceiveItemPo, bool>> exp = z => 1 == 1;
                #region 查询条件
                exp = await InitExp(input, exp);
                #endregion
                IQueryable<RecognizeReceiveItemPo> baseQuery = _db.RecognizeReceiveItems.Where(exp).AsNoTracking();

                // 转货款状态过滤
                if (input.TransferStatus.HasValue)
                {
                    if (input.TransferStatus.Value == 1) // 已转货款
                    {
                        baseQuery = baseQuery.Where(p => p.Classify == RecognizeReceiveClassifyEnum.Temp && !string.IsNullOrEmpty(p.RelateCode));
                    }
                    else // 未转货款
                    {
                        baseQuery = baseQuery.Where(p => p.Classify == RecognizeReceiveClassifyEnum.Temp && string.IsNullOrEmpty(p.RelateCode));
                    }
                }
                var sql = baseQuery.ToQueryString();

                int auditingCount = await baseQuery.Where(t => t.Status == RecognizeReceiveItemStatusEnum.Canceled).CountAsync();
                int waitSubmitCount = await baseQuery.Where(t => t.Status == RecognizeReceiveItemStatusEnum.WaitSubmit && t.CreatedBy == input.Username).CountAsync();
                int waitExecuteCount = await baseQuery.Where(t => t.Status == RecognizeReceiveItemStatusEnum.Auditing).CountAsync();
                int completedCount = await baseQuery.Where(t => t.Status == RecognizeReceiveItemStatusEnum.Completed).CountAsync();
                int allCount = await baseQuery.CountAsync();

                if (input.Status.HasValue)
                {
                    if (input.Status == (int)RecognizeReceiveItemStatusEnum.WaitSubmit)
                    {
                        baseQuery = baseQuery.Where(p => p.Status == (RecognizeReceiveItemStatusEnum)input.Status.Value && p.CreatedBy == input.Username);
                    }
                    else
                    {
                        baseQuery = baseQuery.Where(p => p.Status == (RecognizeReceiveItemStatusEnum)input.Status.Value);
                    }
                }

                var queryLst = baseQuery.Select(z => new RecognizeReceiveItemOutPut
                {
                    BillDate = z.BillDate,
                    Code = z.Code,
                    Id = z.Id,
                    Value = z.Classify == RecognizeReceiveClassifyEnum.Goods ? z.RecognizeReceiveDetails.Sum(p => p.Value) : z.RecognizeReceiveTempDetails.Sum(p => p.Value),
                    Type = z.Type,
                    ReceiveCode = z.ReceiveCode,
                    ReceiveDate = z.ReceiveDate,
                    AttachFileIds = z.AttachFileIds,
                    BusinessDepId = z.BusinessDepId,
                    BusinessDeptFullName = z.BusinessDeptFullName,
                    BusinessDeptFullPath = z.BusinessDeptFullPath,
                    CompanyId = z.CompanyId,
                    CompanyName = z.CompanyName,
                    CustomerId = z.CustomerId,
                    CustomerNme = z.CustomerNme,
                    ReceiveValue = z.ReceiveValue,
                    Status = z.Status,
                    CreatedTime = z.CreatedTime,
                    CreatedBy = z.CreatedBy,
                    Classify = z.Classify,
                    RelateCode = z.RelateCode,
                    ActualCustomerIds = z.RecognizeReceiveDetails.Select(p => p.CustomerId),
                    Settletype = z.Settletype,
                    DraftBillExpireDate = z.DraftBillExpireDate,
                    ProjectCode = z.ProjectCode,
                    ProjectName = z.ProjectName,
                    BankNum = z.BankNum,
                    BankName = z.BankName,
                    BizTime = z.BizTime, //交易时间
                    RemainingRecognizableAmount = z.RemainingAmount ?? 0, //剩余认款金额（使用数据库冗余字段）
                });
                //总条数
                var count = await baseQuery.CountAsync();

                //分页
                var list = await queryLst.OrderByDefault(input.sort).Skip((input.page - 1) * input.limit).Take(input.limit).ToListAsync();

                // 计算转货款状态
                foreach (var item in list)
                {
                    if (item.Classify == RecognizeReceiveClassifyEnum.Temp)
                    {
                        // 暂收款：有RelateCode就是已转货款，否则未转货款
                        item.TransferStatus = !string.IsNullOrEmpty(item.RelateCode) ? 1 : 0;
                    }
                    else
                    {
                        // 货款类型的转货款状态始终为0（未转货款）
                        item.TransferStatus = 0;
                    }
                }

                return new PageResponse<RecognizeReceiveItemOutPut>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        /// <summary>
        /// 获取认款清单（页签数量）
        /// </summary>
        /// <returns></returns>
        public async Task<BaseResponseData<RecognizeReceiveItemTabCount>> GetTabCountByService(RecognizeReceiveItemInput input)
        {
            var ret = BaseResponseData<RecognizeReceiveItemTabCount>.Success("获取成功");
            try
            {
                Expression<Func<RecognizeReceiveItemPo, bool>> exp = z => 1 == 1;
                #region 查询条件 
                exp = await InitExp(input, exp);
                #endregion
                IQueryable<RecognizeReceiveItemPo> baseQuery = _db.RecognizeReceiveItems.Where(exp).AsNoTracking();
                //var sql = baseQuery.ToQueryString();

                var list = await baseQuery.ToListAsync();
                ret.Data = new RecognizeReceiveItemTabCount
                {
                    allCount = list.Count(),
                    completedCount = list.Where(t => t.Status == RecognizeReceiveItemStatusEnum.Completed).Count(),
                    auditingCount = list.Where(t => t.Status == RecognizeReceiveItemStatusEnum.Canceled).Count(),
                    waitExecuteCount = list.Where(t => t.Status == RecognizeReceiveItemStatusEnum.Auditing).Count(),
                    partCancel = list.Where(t => t.Status == RecognizeReceiveItemStatusEnum.PartCanceled).Count(),
                    waitSubmitCount = list.Where(t => t.Status == RecognizeReceiveItemStatusEnum.WaitSubmit && t.CreatedBy == input.Username).Count()
                };
                return ret;
            }
            catch (Exception ex)
            {
                ret.Data = new RecognizeReceiveItemTabCount
                {
                    allCount = 0,
                    completedCount = 0,
                    auditingCount = 0,
                    waitExecuteCount = 0,
                    waitSubmitCount = 0,
                    partCancel = 0
                };
                ret.Message = ex.Message;
                return ret;
            }
        }

        private async Task<Expression<Func<RecognizeReceiveItemPo, bool>>> InitExp(RecognizeReceiveItemInput query, Expression<Func<RecognizeReceiveItemPo, bool>> exp)
        {
            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                exp = exp.And(z => 1 != 1);
                return exp;
            }


            //获取用户数据策略
            var input = new StrategyQueryInput() { userId = user.Data.List.First().Id, functionUri = "metadata://fam" };
            var strategry = await _pcApiClient.GetStrategyAsync(input);
            if (strategry != null)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company"))
                {
                    exp = exp.And(z => 1 != 1);
                    return exp;
                }
                foreach (var key in strategry.RowStrategies.Keys)
                {
                    if (key.ToLower() == "company")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var strategList = strategry.RowStrategies[key].ToHashSet();
                            exp = exp.And(t => strategList.Contains(t.CompanyId));
                        }
                    }
                    if (key == "accountingDept")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var strategList = strategry.RowStrategies[key].ToHashSet();
                            exp = exp.And(t => strategList.Contains(t.BusinessDepId.ToString()));
                        }
                    }
                    if (key == "customer")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var strategList = strategry.RowStrategies[key].ToHashSet();
                            exp = exp.And(t => string.IsNullOrEmpty(t.CustomerId) || strategList.Contains(t.CustomerId));
                        }
                    }
                }
            }

            if (user.Data.List.First().InstitutionType == 4)
            {
                if (user.Data.List.First().Institutions.Any())
                {
                    var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id).ToList();
                    if (serviceIds != null)
                    {
                        // 业务单元缺省值
                        //query.ServiceId ??= serviceIds.FirstOrDefault().Value;
                        query.ServiceIds = serviceIds;
                        if (serviceIds.ToHashSet().Contains(query.ServiceId))
                        {
                            query.ServiceIds = new List<Guid?> { query.ServiceId };
                        }
                        //exp = exp.And(t => serviceIds.AsQueryable().Contains(query.ServiceId));
                    }
                }
                //if (query.BillDateStart == null && query.BillDateEnd == null)
                //{
                //    DateTime firstDayOfMonth = DateTime.Now.AddDays(1 - DateTime.Now.Day);
                //    DateTime lastDayOfNow = DateTime.Now.AddDays(1);
                //    exp = exp.And(z => (z.BillDate != null && z.BillDate >= firstDayOfMonth && z.BillDate <= lastDayOfNow));
                //}
                if (!string.IsNullOrEmpty(query.CreatedByName))
                {
                    var userRet = await _bDSApiClient.GetSmallUsersByDisplayNames(new List<string> { query.CreatedByName.Trim() });
                    var userNames = userRet.Select(t => t.Name).ToList();
                    if (userNames != null && userNames.Any())
                    {
                        exp = exp.And(z => userNames.ToHashSet().Contains(z.CreatedBy));
                    }
                    else
                    {
                        exp = exp.And(z => false);
                        return exp;
                    }
                }
            }
            #region 条件
            if (!string.IsNullOrEmpty(query.Receivedetailcode))
            {
                query.DetailCodes.Add(query.Receivedetailcode);
            }
            if (query.DetailCodes != null && query.DetailCodes.Any())
            {
                var itemIds = await _db.RecognizeReceiveDetails.Where(p => query.DetailCodes.Contains(p.Code)).Select(p => p.RecognizeReceiveItemId).ToListAsync();
                if (itemIds != null && itemIds.Any())
                {
                    exp = exp.And(p => itemIds.ToHashSet().Contains(p.Id));
                }
                else
                {
                    exp = exp.And(p => 1 != 1);
                }
            }
            else if (query.DetailType.HasValue)
            {
                var itemIds = await _db.RecognizeReceiveDetails.Where(p => p.Type == query.DetailType).Select(p => p.RecognizeReceiveItemId).ToListAsync();
                if (itemIds != null && itemIds.Any())
                {
                    exp = exp.And(p => itemIds.ToHashSet().Contains(p.Id));
                }
                else
                {
                    exp = exp.And(p => 1 != 1);
                }
            }
            if (!string.IsNullOrEmpty(query.CustomerName))
            {
                var itemIds = await _db.RecognizeReceiveDetails.Where(p => p.CustomerNme.Contains(query.CustomerName)).Select(p => p.RecognizeReceiveItemId).ToListAsync();
                if (itemIds != null && itemIds.Any())
                {
                    exp = exp.And(p => itemIds.ToHashSet().Contains(p.Id));
                }
                else
                {
                    exp = exp.And(p => 1 != 1);
                }
            }
            if (!string.IsNullOrEmpty(query.HospitalName))
            {
                var itemIds = await _db.RecognizeReceiveDetails.Where(p => p.HospitalName.Contains(query.HospitalName)).Select(p => p.RecognizeReceiveItemId).ToListAsync();
                if (itemIds != null && itemIds.Any())
                {
                    exp = exp.And(p => itemIds.ToHashSet().Contains(p.Id));
                }
                else
                {
                    exp = exp.And(p => 1 != 1);
                }
            }
            if (!string.IsNullOrEmpty(query.Classify) && query.Classify != "全部")
            {
                exp = exp.And(p => p.Type == query.Classify);
            }
            if (query.ItemClassify.HasValue)
            {
                exp = exp.And(z => query.ItemClassify.Equals(z.Classify));
            }
            if (!string.IsNullOrEmpty(query.searchKey))
            {
                exp = exp.And(p => EF.Functions.Like(p.Code, $"%{query.searchKey}%") ||
                                         EF.Functions.Like(p.ReceiveCode, $"%{query.searchKey}%"));
            }
            if (query.customers != null && query.customers.Any())
            {
                exp = exp.And(z => query.customers.ToHashSet().Contains(z.CustomerId));
            }
            if (query.CustomerId.HasValue)
            {
                exp = exp.And(p => p.CustomerId.ToUpper() == query.CustomerId.Value.ToString().ToUpper());
            }
            if (query.CompanyId.HasValue)
            {
                exp = exp.And(p => p.CompanyId.ToUpper() == query.CompanyId.Value.ToString().ToUpper());
            }
            if (!string.IsNullOrEmpty(query.department))
            {
                exp = exp.And(p => EF.Functions.Like(p.BusinessDeptFullPath, $"%{query.department}%"));
            }
            if (query.ServiceIds != null && query.ServiceIds.Any())
            {
                var itemIds = await GetDetailsByServiceIdAsync(new RecognizeReceiveItemInput()
                {
                    ServiceIds = query.ServiceIds,
                    Code = query.Code,
                    ServiceId = query.ServiceId
                });
                if (itemIds != null && itemIds.Any())
                {
                    exp = exp.And(z => itemIds.ToHashSet().Contains(z.Id));
                }
                else { exp = exp.And(z => 1 != 1); }
                //exp = exp.And(p => p.ServiceId == query.ServiceId);
            }
            if (!string.IsNullOrEmpty(query.Code))
            {
                exp = exp.And(p => EF.Functions.Like(p.Code, $"%{query.Code}%"));
            }
            if (!string.IsNullOrEmpty(query.Receivecode))
            {
                exp = exp.And(p => EF.Functions.Like(p.ReceiveCode, $"%{query.Receivecode}%"));
            }
            if (query.BillDateStart.HasValue && query.BillDateEnd.HasValue)
            {
                exp = exp.And(p => p.BillDate >= query.BillDateStart.Value && p.BillDate <= query.BillDateEnd);
            }
            if (query.Status.HasValue)
            {
                if (query.Status == (int)RecognizeReceiveItemStatusEnum.WaitSubmit)
                {
                    exp = exp.And(p => p.Status == (RecognizeReceiveItemStatusEnum)query.Status.Value && p.CreatedBy == query.Username);
                }
                else
                {
                    exp = exp.And(p => p.Status == (RecognizeReceiveItemStatusEnum)query.Status.Value);
                }
            }
            if (user.Data.List.First().InstitutionType == 4)
            {
                IQueryable<RecognizeReceiveItemPo> baseQuery = _db.RecognizeReceiveItems.Include(x => x.RecognizeReceiveDetails).Where(exp).AsNoTracking();
                var list = await baseQuery.ToListAsync();
                var ids = list.Select(x => x.Id).ToList();
                if (user.Data.List.First().Institutions.Any())
                {
                    // 在收款单清单，如果此认款单下的应收对应的应付为销售账期，则不显示此认款单
                    var exclueIds = new List<Guid>();
                    var partInfo = await (from rrdc in _db.RecognizeReceiveDetailCredits
                                          join c in _db.Credits on rrdc.CreditCode equals c.BillCode
                                          join dd in _db.DebtDetails on c.Id equals dd.CreditId
                                          where dd.AccountPeriodType == 2 && ids.Any(p => p == rrdc.RecognizeReceiveItemId)
                                          select new RecognizeReceivePartInfo
                                          {
                                              Id = rrdc.RecognizeReceiveItemId,
                                              CreditCode = c.BillCode,
                                              ServiceId = c.ServiceId.HasValue ? c.ServiceId.Value.ToString() : string.Empty,
                                              CompanyId = c.CompanyId.HasValue ? c.CompanyId.Value.ToString() : string.Empty,
                                              CustomerId = c.CustomerId.HasValue ? c.CustomerId.Value.ToString() : string.Empty
                                          }).ToListAsync();
                    if (partInfo != null && partInfo.Any())
                    {
                        var groupedResults = partInfo.GroupBy(p => new { p.CompanyId, p.CustomerId, p.ServiceId })
                                                         .Select(g => new
                                                         {
                                                             ServiceId = g.Key.ServiceId,
                                                             CompanyId = g.Key.CompanyId,
                                                             CustomerId = g.Key.CustomerId,
                                                             Items = g.ToList()
                                                         })
                                                         .ToList();
                        //#107793 在收款单清单，如果此认款单下的应收对应的应付为销售账期，则不显示此认款单
                        //以上限制需要在采购子系统中做成配置，名称：“屏蔽销售账期认款信息”，默认为是。当为【否】时，此供应商+公司+客户的销售账期的认款信息放开展示。
                        var hiddenSaleAccountInfo = false;
                        foreach (var group in groupedResults)
                        {
                            //查询是否采购子系统配置
                            var puaInput = new SubSysRelaQueryInput()
                            {
                                ServiceId = group.ServiceId,
                                CompanyId = group.CompanyId,
                                CustomerId = group.CustomerId,
                                UserId = _appServiceContextAccessor.Get().UserId
                            };
                            var puaRet = await _purchaseExcuteApiClient.GetSubSysRelaConfig(puaInput);
                            if (puaRet.Code == CodeStatusEnum.Success && puaRet.Data != null && puaRet.Data.List != null && puaRet.Data.List.Any())
                            {
                                hiddenSaleAccountInfo = puaRet.Data.List[0].AuditControls.HiddenSaleAccountInfo.HasValue && !puaRet.Data.List[0].AuditControls.HiddenSaleAccountInfo.Value ? false : true;
                            }
                            if (hiddenSaleAccountInfo)
                            {
                                exclueIds.AddRange(group.Items.Select(x => x.Id));
                            }
                        }
                    }
                    if (exclueIds != null && exclueIds.Any())
                    {
                        exp = exp.And(p => !exclueIds.ToHashSet().Contains(p.Id));
                    }
                }
            }
            return exp;
            #endregion
        }

        /// <summary>
        /// 根据ServiceId获取认款明细数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<List<Guid>> GetDetailsByServiceIdAsync(RecognizeReceiveItemInput query)
        {
            if (query.ServiceIds == null || !query.ServiceIds.Any())
            {
                return new List<Guid>();
            }
            var dictionaryOutputs = await _bDSApiClient.GetDataDictionaryListByType("NoDisplayPriceForTG");
            var newStrs = dictionaryOutputs.Select(x => x.DictionaryName.ToUpper()).ToList();
            var list = await _db.RecognizeReceiveDetails.Include(x => x.RecognizeReceiveItem).Where(z => z.ServiceId.HasValue && query.ServiceIds.Contains(z.ServiceId) && z.Type != 2).AsNoTracking().ToListAsync();
            var retItemIds = new List<Guid>();
            var invoiceDetails = list.Where(p => p.Type == 1).ToList();
            if (invoiceDetails != null)
            {
                var invoiceNos = invoiceDetails.Select(p => p.Code).ToHashSet();
                var invoiceCredits = await _db.InvoiceCredits.Include(p => p.Credit).Where(p => invoiceNos.Contains(p.InvoiceNo)).ToListAsync();
                var currentlist = await _db.Invoices.Where(x => invoiceNos.ToHashSet().Contains(x.InvoiceNo)).AsNoTracking().ToListAsync();
                // 1.终端医院为空直接展示，终端医院等于客户显示
                var ids = new List<Guid>();
                var partIds1 = currentlist.Where(x => string.IsNullOrEmpty(x.HospitalName) || x.CustomerName == x.HospitalName).Select(x => x.Id).ToList();
                ids.AddRange(partIds1);
                // 2.托管公司配置里存在的不显示
                var partlist = currentlist.Where(x => !string.IsNullOrEmpty(x.HospitalName) && x.CustomerName != x.HospitalName).ToList();
                //var invoiceNos = partlist.Select(x => x.InvoiceNo).ToList();
                if (invoiceNos != null && invoiceNos.Any() && newStrs.Any())
                {
                    var restrictedInvoiceNos = await (from ic in _db.InvoiceCredits
                                                      join c in _db.Credits on ic.CreditId equals c.Id into icGroup
                                                      from c in icGroup.DefaultIfEmpty()
                                                      where c.ServiceId.HasValue && c.CompanyId.HasValue && c.CustomerId.HasValue && invoiceNos.ToHashSet().Contains(ic.InvoiceNo) && newStrs.ToHashSet().Contains(c.ServiceId.Value.ToString().ToUpper() + "_" + c.CompanyId.Value.ToString().ToUpper() + "_" + c.CustomerId.Value.ToString().ToUpper())
                                                      select ic.InvoiceNo).ToListAsync();
                    if (restrictedInvoiceNos != null && restrictedInvoiceNos.Any())
                    {
                        var partIds = partlist.Where(x => !restrictedInvoiceNos.ToHashSet().Contains(x.InvoiceNo)).Select(x => x.Id).ToList();
                        ids.AddRange(partIds);
                    }
                    else
                    {
                        var partIds = partlist.Select(x => x.Id).ToList();
                        ids.AddRange(partIds);
                    }
                }
                if (ids.Any())
                {
                    var fpNos = currentlist.Where(x => ids.ToHashSet().Contains(x.Id)).Select(x => x.InvoiceNo).ToList();
                    retItemIds.AddRange(invoiceDetails.Where(x => fpNos.Contains(x.Code)).Select(x => x.RecognizeReceiveItemId));
                }
            }
            //初始应收
            var initCreditDetails = list.Where(p => p.Type == 3).ToList();
            if (initCreditDetails != null)
            {
                var codes = initCreditDetails.Select(x => x.Code).ToList();
                var currentlist = await _db.Credits.Where(x => codes.ToHashSet().Contains(x.BillCode)).AsNoTracking().ToListAsync();
                // 1.终端医院为空直接展示，终端医院等于客户显示
                var ids = new List<Guid>();
                var partIds1 = currentlist.Where(x => string.IsNullOrEmpty(x.HospitalName) || x.CustomerName == x.HospitalName).Select(x => x.Id).ToList();
                ids.AddRange(partIds1);
                // 2.托管公司配置里存在的不显示
                var partIds2 = currentlist.Where(x => !string.IsNullOrEmpty(x.HospitalName) && x.CustomerName != x.HospitalName && x.ServiceId.HasValue && x.CompanyId.HasValue && x.CustomerId.HasValue && !newStrs.Contains(x.ServiceId.Value.ToString().ToUpper() + "_" + x.CompanyId.Value.ToString().ToUpper() + "_" + x.CustomerId.Value.ToString().ToUpper())).Select(x => x.Id).ToList();
                ids.AddRange(partIds2);
                if (ids.Any())
                {
                    var ysNos = currentlist.Where(x => ids.ToHashSet().Contains(x.Id)).Select(x => x.BillCode).ToList();
                    retItemIds.AddRange(initCreditDetails.Where(x => ysNos.Contains(x.Code)).Select(x => x.RecognizeReceiveItemId));
                }
            }
            return retItemIds;
        }

        public async Task<BaseResponseData<bool>> IsExists(IsExistsInput input)
        {
            var ret = BaseResponseData<bool>.Success("操作成功");
            var query = _db.RecognizeReceiveDetails.Include(p => p.RecognizeReceiveItem).Where(p => p.RecognizeReceiveItem.Status != RecognizeReceiveItemStatusEnum.Canceled);
            if (!string.IsNullOrEmpty(input.OrderNo))
            {
                query = query.Where(p => p.Code.Equals(input.OrderNo) && p.Type == 2);
            }
            var count = await query.CountAsync();
            if (count > 0)
            {
                ret.Data = true;
            }
            else
            {
                ret.Data = false;
            }
            return ret;
        }
        /// <summary>
        /// 根据认款单号或者销售单号查询认款信息
        /// </summary>
        /// <returns></returns>
        public async Task<BaseResponseData<List<RecognizeReceiveBatchQueryByCodeOutput>>> BatchQueryRecognizeReceiveByCode(RecognizeReceiveBatchQueryByCodeInput input)
        {
            var ret = new BaseResponseData<List<RecognizeReceiveBatchQueryByCodeOutput>>
            {
                Code = CodeStatusEnum.Success,
                Message = "操作成功",
            };

            // 参数校验优化
            if (input == null || input.Code == null || input.Code.Count == 0)
            {
                ret.Code = CodeStatusEnum.ParamFailed;
                ret.Message = "请提供有效的认款单或销售订单号列表";
                return ret;
            }

            try
            {
                // 异步获取数据
                var codeSet = input.Code.ToHashSet();

                var queryLst = await (
                                from rrdc in _db.RecognizeReceiveDetailCredits.AsNoTracking()
                                join c in _db.Credits.AsNoTracking() on rrdc.CreditId equals c.Id into cGroup//需要取应收表OrderNo销售订单号
                                from c in cGroup.DefaultIfEmpty()
                                join rrd in _db.RecognizeReceiveDetails.AsNoTracking() on rrdc.RecognizeReceiveDetailId equals rrd.Id into rrdGroup
                                from rrd in rrdGroup.DefaultIfEmpty()
                                join rri in _db.RecognizeReceiveItems.AsNoTracking() on rrd.RecognizeReceiveItemId equals rri.Id into rriGroup
                                from rri in rriGroup.DefaultIfEmpty()
                                    //返回认款状态为完成或者部分撤销非撤销部分数据
                                where (rri.Status == RecognizeReceiveItemStatusEnum.Completed || (rri.Status == RecognizeReceiveItemStatusEnum.PartCanceled && rrd.Status == RecognizeReceiveDetailEnum.Normal))
                                      && (codeSet.Contains(rrd.Code) || codeSet.Contains(rri.Code) || codeSet.Contains(c.OrderNo))
                                select new RecognizeReceiveBatchQueryByCodeOutput
                                {
                                    Code = rri.Code,
                                    SaleCode = c.OrderNo,
                                    ReceiveDate = rri.ReceiveDate,
                                    ReceiveAmount = rrd.Value,
                                    Receiver = rri.CreatedBy,
                                    Type = rrd.Type,
                                    ReceiveCode = rri.ReceiveCode,
                                    Status = rri.Status,
                                    DetailStatus = rrd.Status,
                                    CreditCode = rrdc.CreditCode,
                                    CurrentValue = rrdc.CurrentValue,
                                    InvoiceNo = rrdc.InvoiceNo
                                }
                            ).ToListAsync();

                ret.Total = queryLst.Count;
                ret.Data = queryLst;
                return ret;
            }
            catch (Exception ex)
            {
                ret.Code = CodeStatusEnum.Failed;
                ret.Message = $"查询异常: {ex.Message}";
                ret.Data = null;
                return ret;
            }
        }

        /// <summary>
        /// 获取导出数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<RecognizeReceiveDetailExportOutput>, int)> GetExportDetails(ExportDetailsInput input)
        {
            try
            {
                var list = new List<RecognizeReceiveDetailExportOutput>();
                var rrds = await _db.RecognizeReceiveDetails.Include(x => x.RecognizeReceiveDetailCredits).Where(x => x.RecognizeReceiveItemId == input.Id).AsNoTracking().ToListAsync();
                var rrdcs = rrds.SelectMany(x => x.RecognizeReceiveDetailCredits).ToList();
                var creditCodes = rrdcs.Select(x => x.CreditCode).ToHashSet();
                var credits = await _db.Credits.Where(x => !string.IsNullOrEmpty(x.BillCode) && creditCodes.Contains(x.BillCode)).AsNoTracking().ToListAsync();
                foreach (var rrd in rrds)
                {
                    var model = new RecognizeReceiveDetailExportOutput();
                    model.Id = rrd.Id;
                    model.Code = rrd.Code;
                    model.Type = rrd.Type;
                    model.RecognizeDate = rrd.RecognizeDate;
                    model.Value = rrd.Value;
                    model.IsSkip = rrd.IsSkip;
                    model.Note = rrd.Note;
                    model.CreatedBy = rrd.CreatedBy;
                    model.CustomerNme = rrd.CustomerNme;
                    model.Classify = rrd.Classify;
                    model.HospitalId = rrd.HospitalId;
                    model.HospitalName = rrd.HospitalName;
                    model.Note = rrd.Note;
                    model.Status = rrd.Status;
                    model.ServiceId = rrd.ServiceId;
                    model.ServiceName = rrd.ServiceName;
                    model.BackDateTime = rrd.BackDateTime;
                    if (input.Type != 2)
                    {
                        list.Add(model);
                        continue;
                    }
                    var currentrrdcs = rrd.RecognizeReceiveDetailCredits;
                    if (currentrrdcs != null && currentrrdcs.Any())
                    {
                        foreach (var item in currentrrdcs)
                        {
                            //应收信息
                            var credit = credits.FirstOrDefault(x => x.BillCode == item.CreditCode);
                            if (credit == null)
                            {
                                list.Add(model);
                                continue;
                            }
                            model = new RecognizeReceiveDetailExportOutput();
                            model.Id = rrd.Id;
                            model.Code = rrd.Code;
                            model.Type = rrd.Type;
                            model.RecognizeDate = rrd.RecognizeDate;
                            model.Value = rrd.Value;
                            model.IsSkip = rrd.IsSkip;
                            model.Note = rrd.Note;
                            model.CreatedBy = rrd.CreatedBy;
                            model.CustomerNme = rrd.CustomerNme;
                            model.Classify = rrd.Classify;
                            model.HospitalId = rrd.HospitalId;
                            model.HospitalName = rrd.HospitalName;
                            model.Note = rrd.Note;
                            model.Status = rrd.Status;
                            model.ServiceId = rrd.ServiceId;
                            model.ServiceName = rrd.ServiceName;
                            model.BackDateTime = rrd.BackDateTime;
                            model.CreditCode = item.CreditCode;
                            model.CreditType = credit.CreditType;
                            model.BillDate = credit.BillDate;
                            model.OrderNo = string.IsNullOrEmpty(item.OrderNo) ? item.OrderNo : credit.OrderNo;
                            model.ProjectNameByCredit = credit.ProjectName;
                            model.ServiceNameByCredit = credit.ServiceName;
                            model.CreditValue = credit.Value;
                            model.CurrentValueByCredit = item.CurrentValue;
                            list.Add(model);
                        }
                    }
                    else
                    {
                        list.Add(model);
                    }
                }
                return (list, list.Count);
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// 获取供应商退款冲销列表
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<AgentRefundAbatementOutput>> GetAgentRefundAbatements(AgentRefundAbatementInput input)
        {
            try
            {
                Expression<Func<DebtPo, bool>> exp = z => 1 == 1;
                #region 查询条件 
                var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
                {
                    Names = new List<string> { _appServiceContextAccessor.Get().UserName }
                });
                if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
                {
                    exp = exp.And(z => 1 != 1);
                }

                //获取用户数据策略
                var inputStrategy = new StrategyQueryInput() { userId = user.Data.List.First().Id, functionUri = "metadata://fam" };
                var strategry = await _pcApiClient.GetStrategyAsync(inputStrategy);
                if (strategry != null)
                {
                    var rowStrategies = strategry.RowStrategies;
                    if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company"))
                    {
                        exp = exp.And(z => 1 != 1);
                    }
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key.ToLower() == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].Select(z => z.ToUpper()).ToHashSet();
                                exp = exp.And(t => strategList.AsQueryable().Contains(t.CompanyId.ToString()));
                            }
                        }
                        else if (key == "accountingDept")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].Select(z => z.ToUpper()).ToHashSet();
                                exp = exp.And(t => strategList.AsQueryable().Contains(t.BusinessDeptId.ToString()));
                            }
                        }
                        else if (key.ToLower() == "project")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].Select(z => z.ToUpper()).ToList();
                                exp = exp.And(t => !t.ProjectId.HasValue || strategList.Contains(t.ProjectId.ToString()));
                            }
                        }
                    }
                }
                if (!string.IsNullOrEmpty(input.DebtCode))
                {
                    exp = exp.And(z => z.BillCode == input.DebtCode);
                }
                #endregion
                IQueryable<AgentRefundAbatementOutput> baseQuery = from abatement in _db.Abatements
                                                                   join debt in _db.Debts.Where(exp) on abatement.CreditBillCode equals debt.BillCode
                                                                   where abatement.CreditType == "debt" &&
                                                                         abatement.DebtType == "receive" &&
                                                                         (string.IsNullOrEmpty(input.ReceiveCode) ? true : abatement.DebtBillCode == input.ReceiveCode) &&
                                                                         (string.IsNullOrEmpty(input.Department) ? true : debt.BusinessDeptId == input.Department) &&
                                                                         (!input.CompanyId.HasValue ? true : debt.CompanyId == input.CompanyId) &&
                                                                         (!input.AgentId.HasValue ? true : debt.AgentId == input.AgentId) &&
                                                                         (string.IsNullOrEmpty(input.ProjectNo) ? true : debt.ProjectCode == input.ProjectNo)
                                                                   orderby abatement.Abtdate descending
                                                                   select new AgentRefundAbatementOutput
                                                                   {
                                                                       AbatementCreatedBy = abatement.CreatedBy,
                                                                       AbatementDate = abatement.Abtdate.ToString("yyyy-MM-dd"),
                                                                       AbatementValue = abatement.Value,
                                                                       AgentName = debt.AgentName,
                                                                       BusinessDeptFullName = debt.BusinessDeptFullName,
                                                                       CompanyName = debt.CompanyName,
                                                                       DebtCode = debt.BillCode,
                                                                       ProjectName = debt.ProjectName,
                                                                       ReceiveCode = abatement.DebtBillCode,
                                                                       //ReceiveDate = receive.ReceiveDate.ToString("yyyy-MM-dd"),
                                                                       //ReceiveValue = receive.ReceiveValue,
                                                                   };

                //总条数
                var count = await baseQuery.CountAsync();

                //分页
                var list = await baseQuery.Skip((input.page - 1) * input.limit).Take(input.limit).ToListAsync();
                return new PageResponse<AgentRefundAbatementOutput>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }


    }
}
